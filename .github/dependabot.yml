version: 2
updates:
  # Enable version updates for npm
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10
    reviewers:
      - "mayurchavhan"
    assignees:
      - "mayurchavhan"
    commit-message:
      prefix: "npm"
      prefix-development: "npm-dev"
      include: "scope"
    labels:
      - "dependencies"
      - "npm"
    ignore:
      # Ignore major version updates for stable dependencies
      - dependency-name: "react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "react-dom"
        update-types: ["version-update:semver-major"]
    groups:
      react:
        patterns:
          - "react*"
          - "@types/react*"
      testing:
        patterns:
          - "*jest*"
          - "*testing*"
          - "vitest*"
          - "@vitest/*"
      eslint:
        patterns:
          - "eslint*"
          - "@eslint/*"
      typescript:
        patterns:
          - "typescript*"
          - "@types/*"

  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 5
    reviewers:
      - "mayurchavhan"
    assignees:
      - "mayurchavhan"
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"

  # Enable version updates for Docker
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 3
    reviewers:
      - "mayurchavhan"
    assignees:
      - "mayurchavhan"
    commit-message:
      prefix: "docker"
      include: "scope"
    labels:
      - "dependencies"
      - "docker"
