name: Build, Test & Deploy Portfolio

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    name: Test Application
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting
      run: npm run lint

    - name: Build application
      run: npm run build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-files
        path: dist/
        retention-days: 1

  docker-build:
    name: Build Docker Image
    needs: test
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  docker-test:
    name: Test Docker Container
    needs: docker-build
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build test image
      uses: docker/build-push-action@v5
      with:
        context: .
        load: true
        tags: portfolio-test:latest
        cache-from: type=gha

    - name: Test container startup
      run: |
        # Start container in background
        docker run -d --name portfolio-test -p 8080:8080 portfolio-test:latest

        # Wait for container to be ready
        timeout 60s bash -c 'until curl -f http://localhost:8080/health; do sleep 2; done'

        # Test main page
        curl -f http://localhost:8080/ | grep -q "Mayur Chavhan"

        # Test static assets
        curl -f -I http://localhost:8080/favicon.svg

        # Stop container
        docker stop portfolio-test

    - name: Check container security
      run: |
        # Check if container runs as non-root
        USER_ID=$(docker run --rm portfolio-test:latest id -u)
        if [ "$USER_ID" = "0" ]; then
          echo "Warning: Container is running as root"
          exit 1
        fi
        echo "Container runs as user ID: $USER_ID"

  deploy-staging:
    name: Deploy to Staging
    needs: [ test, docker-test ]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment steps here
        # For example, updating a staging server with the new image

  deploy-production:
    name: Deploy to Production
    needs: [ test, docker-test ]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment steps here
        # For example, updating a production server with the new image
