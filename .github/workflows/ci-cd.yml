name: 🚀 Build, Test & Deploy Portfolio

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
  # Run security scans weekly on Sundays at 2 AM UTC
  - cron: '0 2 * * 0'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  NODE_VERSION: '18'

jobs:
  # Code Quality & Security Checks
  code-quality:
    name: 🔍 Code Quality & Security
    runs-on: ubuntu-latest
    if: github.event_name != 'schedule'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run ESLint
      run: npm run lint

    - name: Check code formatting
      run: npm run format:check

    - name: Type checking
      run: npm run type-check

    - name: Run security audit
      run: npm audit --audit-level=moderate

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: javascript

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3

  # Comprehensive Testing
  test:
    name: 🧪 Test Application
    runs-on: ubuntu-latest
    needs: code-quality
    if: github.event_name != 'schedule'

    strategy:
      matrix:
        node-version: [ 18, 20 ]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run unit tests
      run: npm run test:run

    - name: Run tests with coverage
      run: npm run test:coverage

    - name: Upload coverage reports
      uses: codecov/codecov-action@v4
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

    - name: Build application
      run: npm run build

    - name: Test build output
      run: |
        # Check if build files exist
        test -f dist/index.html
        test -d dist/assets

        # Check if build is optimized
        if [ $(find dist -name "*.js" -exec wc -c {} + | tail -1 | awk '{print $1}') -gt 5000000 ]; then
          echo "Warning: Build size is larger than expected"
        fi

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-files-node-${{ matrix.node-version }}
        path: dist/
        retention-days: 7

  # Enhanced Docker Build with Security Scanning
  docker-build:
    name: 🐳 Build & Scan Docker Image
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name != 'schedule'
    permissions:
      contents: read
      packages: write
      security-events: write

    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-tags: ${{ steps.meta.outputs.tags }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
        labels: |
          org.opencontainers.image.title=Mayur Chavhan Portfolio
          org.opencontainers.image.description=DevOps Engineer & Cloud Architect Portfolio
          org.opencontainers.image.vendor=Mayur Chavhan
          org.opencontainers.image.licenses=MIT

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        provenance: true
        sbom: true

    - name: Run Trivy vulnerability scanner
      if: github.event_name != 'pull_request'
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      if: github.event_name != 'pull_request'
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Generate SBOM
      if: github.event_name != 'pull_request'
      uses: anchore/sbom-action@v0
      with:
        image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
        format: spdx-json
        output-file: sbom.spdx.json

    - name: Upload SBOM
      if: github.event_name != 'pull_request'
      uses: actions/upload-artifact@v4
      with:
        name: sbom
        path: sbom.spdx.json

  docker-test:
    name: Test Docker Container
    needs: docker-build
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build test image
      uses: docker/build-push-action@v5
      with:
        context: .
        load: true
        tags: portfolio-test:latest
        cache-from: type=gha

    - name: Test container startup
      run: |
        # Start container in background
        docker run -d --name portfolio-test -p 8080:8080 portfolio-test:latest

        # Wait for container to be ready
        timeout 60s bash -c 'until curl -f http://localhost:8080/health; do sleep 2; done'

        # Test main page
        curl -f http://localhost:8080/ | grep -q "Mayur Chavhan"

        # Test static assets
        curl -f -I http://localhost:8080/favicon.svg

        # Stop container
        docker stop portfolio-test

    - name: Check container security
      run: |
        # Check if container runs as non-root
        USER_ID=$(docker run --rm portfolio-test:latest id -u)
        if [ "$USER_ID" = "0" ]; then
          echo "Warning: Container is running as root"
          exit 1
        fi
        echo "Container runs as user ID: $USER_ID"

  # Performance Testing
  performance-test:
    name: 🚀 Performance Testing
    needs: docker-build
    runs-on: ubuntu-latest
    if: github.event_name != 'schedule' && github.event_name != 'pull_request'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build

    - name: Analyze bundle size
      run: |
        echo "## Bundle Size Analysis" >> $GITHUB_STEP_SUMMARY
        echo "| File | Size |" >> $GITHUB_STEP_SUMMARY
        echo "|------|------|" >> $GITHUB_STEP_SUMMARY
        find dist -name "*.js" -o -name "*.css" | while read file; do
          size=$(du -h "$file" | cut -f1)
          echo "| $file | $size |" >> $GITHUB_STEP_SUMMARY
        done

    - name: Run Lighthouse CI
      uses: treosh/lighthouse-ci-action@v10
      with:
        configPath: './.lighthouserc.json'
        uploadArtifacts: true
        temporaryPublicStorage: true

  # Weekly Security Scan
  security-scan:
    name: 🔒 Weekly Security Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run security audit
      run: |
        npm audit --audit-level=low --json > audit-results.json || true

    - name: Run dependency check
      uses: dependency-check/Dependency-Check_Action@main
      with:
        project: 'portfolio-website'
        path: '.'
        format: 'ALL'

    - name: Upload dependency check results
      uses: actions/upload-artifact@v4
      with:
        name: dependency-check-report
        path: reports/

  # Enhanced Staging Deployment
  deploy-staging:
    name: 🚀 Deploy to Staging
    needs: [ test, docker-test, performance-test ]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment:
      name: staging
      url: https://staging.mayurchavhan.com

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "🚀 Deploying to staging environment..."
        echo "Image: ${{ needs.docker-build.outputs.image-tags }}"
        echo "Digest: ${{ needs.docker-build.outputs.image-digest }}"

        # Example deployment commands (customize for your infrastructure)
        # docker pull ${{ needs.docker-build.outputs.image-tags }}
        # docker stop portfolio-staging || true
        # docker rm portfolio-staging || true
        # docker run -d --name portfolio-staging -p 3000:8080 ${{ needs.docker-build.outputs.image-tags }}

    - name: Run smoke tests
      run: |
        echo "Running smoke tests on staging..."
        # Add your smoke tests here
        # curl -f https://staging.mayurchavhan.com/health

    - name: Notify deployment
      if: always()
      run: |
        echo "Staging deployment completed with status: ${{ job.status }}"

  # Enhanced Production Deployment
  deploy-production:
    name: 🌟 Deploy to Production
    needs: [ test, docker-test, performance-test ]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment:
      name: production
      url: https://mayurchavhan.com

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        echo "🌟 Deploying to production environment..."
        echo "Image: ${{ needs.docker-build.outputs.image-tags }}"
        echo "Digest: ${{ needs.docker-build.outputs.image-digest }}"

        # Example blue-green deployment (customize for your infrastructure)
        # docker pull ${{ needs.docker-build.outputs.image-tags }}
        # docker run -d --name portfolio-green -p 3001:8080 ${{ needs.docker-build.outputs.image-tags }}
        #
        # # Health check
        # timeout 60s bash -c 'until curl -f http://localhost:3001/health; do sleep 2; done'
        #
        # # Switch traffic
        # docker stop portfolio-blue || true
        # docker rm portfolio-blue || true
        # docker stop portfolio || true
        # docker run -d --name portfolio -p 8080:8080 ${{ needs.docker-build.outputs.image-tags }}

    - name: Run production smoke tests
      run: |
        echo "Running production smoke tests..."
        # Add your production smoke tests here
        # curl -f https://mayurchavhan.com/health
        # curl -f https://mayurchavhan.com/ | grep -q "Mayur Chavhan"

    - name: Create GitHub release
      if: success()
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ github.run_number }}
        release_name: Release v${{ github.run_number }}
        body: |
          ## Changes in this Release
          - Deployed commit: ${{ github.sha }}
          - Docker image: ${{ needs.docker-build.outputs.image-tags }}
          - Build artifacts: Available in Actions

          ## Deployment Details
          - Environment: Production
          - Deployed at: ${{ github.event.head_commit.timestamp }}
          - Deployed by: ${{ github.actor }}
        draft: false
        prerelease: false

    - name: Notify deployment success
      if: success()
      run: |
        echo "✅ Production deployment successful!"
        echo "🌐 Site URL: https://mayurchavhan.com"
        echo "📊 Monitoring: Check your monitoring dashboard"

    - name: Notify deployment failure
      if: failure()
      run: |
        echo "❌ Production deployment failed!"
        echo "🔍 Check the logs for details"
