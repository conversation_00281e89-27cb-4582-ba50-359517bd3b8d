version: '3.8'

services:
  # Development service with enhanced configuration
  portfolio-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        - NODE_ENV=development
    ports:
      - '3000:3000'
    volumes:
      - .:/app:cached
      - /app/node_modules
      - /app/dist
    environment:
      - NODE_ENV=development
      - VITE_DEV_TOOLS=true
      - VITE_SHOW_PERFORMANCE_METRICS=true
    env_file:
      - .env
    networks:
      - portfolio-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    profiles:
      - dev
    labels:
      - 'traefik.enable=true'
      - 'traefik.http.routers.portfolio-dev.rule=Host(`dev.portfolio.local`)'
      - 'traefik.http.services.portfolio-dev.loadbalancer.server.port=3000'

  # Production service with enhanced configuration
  portfolio-prod:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    ports:
      - '8080:8080'
    environment:
      - NODE_ENV=production
      - VITE_BUILD_SOURCEMAP=false
    env_file:
      - .env
    networks:
      - portfolio-network
    restart: unless-stopped
    healthcheck:
      test:
        [
          'CMD',
          'wget',
          '--no-verbose',
          '--tries=1',
          '--spider',
          '--timeout=5',
          'http://localhost:8080/health',
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    profiles:
      - prod
    labels:
      - 'traefik.enable=true'
      - 'traefik.http.routers.portfolio-prod.rule=Host(`portfolio.local`)'
      - 'traefik.http.services.portfolio-prod.loadbalancer.server.port=8080'
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/cache/nginx
      - /var/run

  # Production with custom port and monitoring
  portfolio:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    ports:
      - '${PORT:-8080}:8080'
    environment:
      - NODE_ENV=production
      - VITE_BUILD_SOURCEMAP=false
    env_file:
      - .env
    networks:
      - portfolio-network
    restart: unless-stopped
    healthcheck:
      test:
        [
          'CMD',
          'wget',
          '--no-verbose',
          '--tries=1',
          '--spider',
          '--timeout=5',
          'http://localhost:8080/health',
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/cache/nginx
      - /var/run
    logging:
      driver: 'json-file'
      options:
        max-size: '10m'
        max-file: '3'
    labels:
      - 'traefik.enable=true'
      - 'traefik.http.routers.portfolio.rule=Host(`mayurchavhan.com`)'
      - 'traefik.http.services.portfolio.loadbalancer.server.port=8080'

networks:
  portfolio-network:
    driver: bridge
    name: portfolio-network
