FROM node:18-alpine

# Add metadata labels
LABEL maintainer="May<PERSON> Chavhan <<EMAIL>>" \
    description="Development environment for Mayur Chavhan Portfolio" \
    version="1.0.0-dev"

# Install security updates and development tools
RUN apk update && apk upgrade && \
    apk add --no-cache \
    git \
    curl \
    wget && \
    rm -rf /var/cache/apk/*

# Create non-root user for development
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Change ownership of the working directory
RUN chown -R nextjs:nodejs /app

# Switch to non-root user
USER nextjs

# Install dependencies first (for better caching)
COPY --chown=nextjs:nodejs package*.json ./
RUN npm ci --no-audit --no-fund

# Copy source code with correct ownership
COPY --chown=nextjs:nodejs . .

# Expose port
EXPOSE 3000

# Add health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000/ || exit 1

# Start development server with hot reload
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "3000"]
