import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, cleanup } from './test/utils';
import App from './App';

// Mock the components to avoid complex rendering issues in tests
vi.mock('./components/Navbar', () => ({
  default: () => <nav data-testid="navbar">Navbar</nav>,
}));

vi.mock('./components/HeroSection', () => ({
  default: () => <section data-testid="hero">Hero</section>,
}));

vi.mock('./components/AboutSection', () => ({
  default: () => <section data-testid="about">About</section>,
}));

vi.mock('./components/SkillsSection', () => ({
  default: () => <section data-testid="skills">Skills</section>,
}));

vi.mock('./components/ProjectsSection', () => ({
  default: () => <section data-testid="projects">Projects</section>,
}));

vi.mock('./components/ExperienceSection', () => ({
  default: () => <section data-testid="experience">Experience</section>,
}));

vi.mock('./components/BlogSection', () => ({
  default: () => <section data-testid="blog">Blog</section>,
}));

vi.mock('./components/ContactSection', () => ({
  default: () => <section data-testid="contact">Contact</section>,
}));

vi.mock('./components/Footer', () => ({
  default: () => <footer data-testid="footer">Footer</footer>,
}));

describe('App', () => {
  beforeEach(() => {
    // Clear any existing schema scripts
    const existingSchemas = document.querySelectorAll(
      'script[type="application/ld+json"]'
    );
    existingSchemas.forEach(script => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    });
  });

  afterEach(() => {
    cleanup();
    // Clean up after each test
    const schemaScripts = document.querySelectorAll(
      'script[type="application/ld+json"]'
    );
    schemaScripts.forEach(script => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    });
  });

  it('renders without crashing', () => {
    render(<App />);
    expect(document.body).toBeInTheDocument();
  });

  it('sets the correct page title', () => {
    render(<App />);
    expect(document.title).toBe(
      'Mayur Chavhan | DevOps Engineer & Cloud Architect'
    );
  });

  it('adds schema markup for SEO', () => {
    render(<App />);
    const schemaScript = document.querySelector(
      'script[type="application/ld+json"]'
    );
    expect(schemaScript).toBeInTheDocument();

    if (schemaScript) {
      const schemaData = JSON.parse(schemaScript.innerHTML);
      expect(schemaData['@context']).toBe('https://schema.org');
      expect(schemaData['@type']).toBe('Person');
      expect(schemaData.name).toBe('Mayur Chavhan');
      expect(schemaData.jobTitle).toBe('DevOps Engineer & Cloud Architect');
    }
  });

  it('renders all main sections', () => {
    render(<App />);

    // Check for main layout structure
    expect(screen.getByRole('main')).toBeInTheDocument();

    // Check that mocked components are rendered
    expect(screen.getByTestId('navbar')).toBeInTheDocument();
    expect(screen.getByTestId('hero')).toBeInTheDocument();
    expect(screen.getByTestId('about')).toBeInTheDocument();
    expect(screen.getByTestId('skills')).toBeInTheDocument();
    expect(screen.getByTestId('projects')).toBeInTheDocument();
    expect(screen.getByTestId('experience')).toBeInTheDocument();
    expect(screen.getByTestId('blog')).toBeInTheDocument();
    expect(screen.getByTestId('contact')).toBeInTheDocument();
    expect(screen.getByTestId('footer')).toBeInTheDocument();

    const mainElement = screen.getByRole('main');
    expect(mainElement).toHaveClass('flex-grow');
  });

  it('has proper semantic HTML structure', () => {
    render(<App />);

    // Check for proper semantic structure
    expect(screen.getByRole('main')).toBeInTheDocument();
    expect(screen.getByTestId('navbar')).toBeInTheDocument();
    expect(screen.getByTestId('footer')).toBeInTheDocument();
  });

  it('applies correct CSS classes for layout', () => {
    render(<App />);

    const appContainer = document.querySelector('.flex.flex-col.min-h-screen');
    expect(appContainer).toBeInTheDocument();

    const mainElement = screen.getByRole('main');
    expect(mainElement).toHaveClass('flex-grow');
  });
});
