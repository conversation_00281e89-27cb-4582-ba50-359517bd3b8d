# Multi-stage build for minimal production image
FROM node:18-alpine AS builder

# Install security updates and build dependencies
RUN apk update && apk upgrade && \
    apk add --no-cache git && \
    rm -rf /var/cache/apk/*

# Create non-root user for build process
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 -G nodejs

# Set working directory
WORKDIR /app

# Change ownership of the working directory
RUN chown -R nextjs:nodejs /app
USER nextjs

# Copy package files with correct ownership
COPY --chown=nextjs:nodejs package*.json ./

# Install dependencies with npm ci for faster, reliable builds
RUN npm ci --only=production=false --no-audit --no-fund

# Copy source code with correct ownership
COPY --chown=nextjs:nodejs . .

# Build the application
RUN npm run build && \
    npm prune --production

# Production stage - use specific version for security
FROM nginx:1.25-alpine

# Add metadata labels
LABEL maintainer="Mayur Chavhan <<EMAIL>>" \
    description="Portfolio website for Mayur Cha<PERSON>han - DevOps Engineer & Cloud Architect" \
    version="1.0.0" \
    org.opencontainers.image.title="Mayur Chavhan Portfolio" \
    org.opencontainers.image.description="DevOps Engineer & Cloud Architect Portfolio" \
    org.opencontainers.image.vendor="Mayur Chavhan" \
    org.opencontainers.image.licenses="MIT"

# Install security updates and required packages
RUN apk update && apk upgrade && \
    apk add --no-cache \
    dumb-init \
    wget \
    curl && \
    rm -rf /var/cache/apk/*

# Create non-root user with specific UID/GID
RUN addgroup -g 1001 -S nginx && \
    adduser -S nginx -u 1001 -G nginx

# Copy built assets from builder stage with correct ownership
COPY --from=builder --chown=nginx:nginx /app/dist /usr/share/nginx/html

# Copy custom nginx configuration
COPY --chown=nginx:nginx nginx.conf /etc/nginx/nginx.conf

# Create necessary directories and set permissions
RUN mkdir -p /var/cache/nginx /var/log/nginx /var/run && \
    chown -R nginx:nginx /var/cache/nginx /var/log/nginx /var/run /etc/nginx

# Remove default nginx config and create pid directory
RUN rm -f /etc/nginx/conf.d/default.conf && \
    touch /var/run/nginx.pid && \
    chown nginx:nginx /var/run/nginx.pid

# Switch to non-root user
USER nginx

# Expose port
EXPOSE 8080

# Add health check with improved configuration
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider --timeout=5 http://localhost:8080/health || exit 1

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]
CMD ["nginx", "-g", "daemon off;"]
